<?php

namespace Tests\Feature;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Package;
use App\Models\Region;
use App\Models\Subregion;
use Database\Seeders\Tests\GeographyTestSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GeographyRelationshipsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed the database with test data
        $this->seed(GeographyTestSeeder::class);
    }

    /**
     * Test continent to subregion bidirectional relationship - 1 level deep
     */
    public function test_continent_subregion_relationship(): void
    {
        // Get a continent with subregions
        $europe = Continent::where('name', 'Europe')->first();

        // Assert the continent has subregions
        $this->assertTrue($europe->subregions->count() > 0);

        // Get a subregion
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Assert the subregion has a continent
        $this->assertTrue($southernEurope->continent->count() > 0);

        // Assert the subregion belongs to the continent
        // Access through relationship
        $this->assertTrue($southernEurope->continent()->get()->contains($europe));
        // Access through magic method
        $this->assertEquals($europe->id, $southernEurope->continent->id);
        // Assert the continent contains the subregion in its children
        // Access through relationship
        $this->assertTrue($europe->subregions()->get()->contains($southernEurope));
        //Access through magic method
        $this->assertTrue($europe->subregions->contains($southernEurope));
    }

    /**
     * Test subregion to country bidirectional relationship - 1 level deep
     */
    public function test_subregion_country_relationship(): void
    {
        // Get a subregion with countries
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Assert the subregion has countries
        $this->assertTrue($southernEurope->countries->count() > 0);

        // Get a country
        $italy = Country::where('name', 'Italy')->first();

        // Assert the country has a subregion
        $this->assertTrue($italy->subregion->count() > 0);

        // Assert the country belongs to the subregion
        // Access through relationship
        $this->assertTrue($italy->subregion()->get()->contains($southernEurope));
        // Access through magic method
        $this->assertEquals($southernEurope->id, $italy->subregion->id);
        // Assert the subregion contains the country in its children
        // Access through relationship
        $this->assertTrue($southernEurope->countries()->get()->contains($italy));
        //Access through magic method
        $this->assertTrue($southernEurope->countries->contains($italy));
    }

    /**
     * Test country to region bidirectional relationship - 1 level deep
     */
    public function test_country_region_relationship(): void
    {
        // Get a country with regions
        $italy = Country::where('name', 'Italy')->first();

        // Assert the country has regions
        $this->assertTrue($italy->regions->count() > 0);

        // Get a region
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Assert the region has a country
        $this->assertTrue($tuscany->country->count() > 0);

        // Assert the region belongs to the country
        // Access through relationship
        $this->assertTrue($tuscany->country()->get()->contains($italy));
        // Access through magic method
        $this->assertEquals($italy->id, $tuscany->country->id);
        // Assert the country contains the region in its children
        // Access through relationship
        $this->assertTrue($italy->regions()->get()->contains($tuscany));
        //Access through magic method
        $this->assertTrue($italy->regions->contains($tuscany));
    }

    /**
     * Test region to city bidirectional relationship - 1 level deep
     */
    public function test_region_city_relationship(): void
    {
        // Get a region with cities
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Assert the region has cities
        $this->assertTrue($tuscany->cities->count() > 0);

        // Get a city
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has a region
        $this->assertTrue($florence->region->count() > 0);

        // Assert the city belongs to the region
        // Access through relationship
        $this->assertTrue($florence->region()->get()->contains($tuscany));
        // Access through magic method
        $this->assertEquals($tuscany->id, $florence->region->id);
        // Assert the region contains the city in its children
        // Access through relationship
        $this->assertTrue($tuscany->cities()->get()->contains($florence));
        //Access through magic method
        $this->assertTrue($tuscany->cities->contains($florence));
    }

    /**
     * Test continent to country bidirectional relationship - 2 level deep
     */
    public function test_continent_country_relationship(): void
    {
        // Get a continent with countries
        $europe = Continent::where('name', 'Europe')->first();

        // Assert the continent has countries
        $this->assertTrue($europe->countries->count() > 0);

        // Get a country
        $italy = Country::where('name', 'Italy')->first();

        // Assert the country has a continent
        $this->assertTrue($italy->continent->count() > 0);

        // Assert the country belongs to the continent
        // Access through relationship
        $this->assertTrue($italy->continent()->get()->contains($europe));
        // Access through magic method
        $this->assertEquals($europe->id, $italy->continent->id);
        // Assert the continent contains the country in its grandchildren
        // Access through relationship
        $this->assertTrue($europe->countries()->get()->contains($italy));
        //Access through magic method
        $this->assertTrue($europe->countries->contains($italy));
    }

    /**
     * Test subregion to region bidirectional relationship - 2 level deep
     */
    public function test_subregion_region_relationship(): void
    {
        // Get a subregion with regions
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Assert the subregion has regions
        $this->assertTrue($southernEurope->regions->count() > 0);

        // Get a region
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Assert the region has a subregion
        $this->assertTrue($tuscany->subregion->count() > 0);

        // Assert the region belongs to the subregion
        // Access through relationship
        $this->assertTrue($tuscany->subregion()->get()->contains($southernEurope));
        // Access through magic method
        $this->assertEquals($southernEurope->id, $tuscany->subregion->id);
        // Assert the subregion contains the region in its grandchildren
        // Access through relationship
        $this->assertTrue($southernEurope->regions()->get()->contains($tuscany));
        //Access through magic method
        $this->assertTrue($southernEurope->regions->contains($tuscany));
    }

    /**
     * Test country to city bidirectional relationship - 2 level deep
     */
    public function test_country_city_relationship(): void
    {
        // Get a country with cities
        $italy = Country::where('name', 'Italy')->first();

        // Assert the country has cities
        $this->assertTrue($italy->cities->count() > 0);

        // Get a city
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has country
        $this->assertTrue($florence->country->count() > 0);

        // Assert the city belongs to the country
        // Access through relationship
        $this->assertTrue($florence->country()->get()->contains($italy));
        // Access through magic method
        $this->assertEquals($italy->id, $florence->country->id);
        // Assert the country contains the city in its grandchildren
        // Access through relationship
        $this->assertTrue($italy->cities()->get()->contains($florence));
        //Access through magic method
        $this->assertTrue($italy->cities->contains($florence));
    }

    /**
     * Test continent to region bidirctional relationship - 3 level deep
     */
    public function test_continent_region_relationship(): void
    {
        // Get a continent with regions
        $europe = Continent::where('name', 'Europe')->first();

        // Assert the continent has regions
        $this->assertTrue($europe->regions->count() > 0);

        // Get a region
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Assert the region has a continent
        $this->assertTrue($tuscany->continent->count() > 0);

        // Assert the region belongs to the continent
        // Access through relationship
        $this->assertTrue($tuscany->continent()->get()->contains($europe));
        // Access through magic method
        $this->assertEquals($europe->id, $tuscany->continent->id);
        // Assert the continent contains the region in its great-grandchildren
        // Access through relationship
        $this->assertTrue($europe->regions()->get()->contains($tuscany));
        //Access through magic method
        $this->assertTrue($europe->regions->contains($tuscany));
    }

    /**
     * Test Subregion to city bidirectional relationship - 3 level deep
     */
    public function test_subregion_city_relationship(): void
    {
        // Get a subregion with cities
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Assert the subregion has cities
        $this->assertTrue($southernEurope->cities->count() > 0);

        // Get a city
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has a subregion
        $this->assertTrue($florence->subregion->count() > 0);

        // Assert the city belongs to the subregion
        // Access through relationship
        $this->assertTrue($florence->subregion()->get()->contains($southernEurope));
        // Access through magic method
        $this->assertEquals($southernEurope->id, $florence->subregion->id);
        // Assert the subregion contains the city in its great-grandchildren
        // Access through relationship
        $this->assertTrue($southernEurope->cities()->get()->contains($florence));
        //Access through magic method
        $this->assertTrue($southernEurope->cities->contains($florence));
    }

    /**
     * Test continent to city bidirectional relationship - 4 level deep
     */
    public function test_continent_city_relationship(): void
    {
        // Get a continent with cities
        $europe = Continent::where('name', 'Europe')->first();

        // Assert the continent has cities
        $this->assertTrue($europe->cities->count() > 0);

        // Get a city
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has a continent
        $this->assertTrue($florence->continent->count() > 0);

        // Assert the city belongs to the continent
        // Access through relationship
        $this->assertTrue($florence->continent()->get()->contains($europe));
        // Access through magic method
        $this->assertEquals($europe->id, $florence->continent->id);
        // Assert the continent contains the city in its great-great-grandchildren
        // Access through relationship
        $this->assertTrue($europe->cities()->get()->contains($florence));
        //Access through magic method
        $this->assertTrue($europe->cities->contains($florence));
    }

    /**
     * Test city to package bidirectional relationship
     */
    public function test_city_package_relationship(): void
    {
        // Get a city with packages
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has packages
        $this->assertTrue($florence->packages->count() > 0);

        // Get a package with cities
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Assert the package has cities
        $this->assertTrue($italianPackage->cities->count() > 0);

        // Assert the relationship is bidirectional
        $this->assertTrue($florence->packages->contains($italianPackage));
        $this->assertTrue($italianPackage->cities->contains($florence));

        // Assert the package is related to Florence
        $this->assertTrue($italianPackage->cities->contains('id', $florence->id));

        // Assert the city is related to Italian Adventure
        $this->assertTrue($florence->packages->contains('id', $italianPackage->id));
    }

    /**
     * Test city to package relationship returns correct number of packages
     */
    public function test_city_package_relationship_count(): void
    {
        // Get a city with packages
        $florence = City::where('name', 'Florence')->first();

        // Assert the city has only 4 packages
        $this->assertEquals(4, $florence->packages->count());
    }

    /**
     * Test city to package relationship returns correct packages
     */
    public function test_city_package_relationship_packages(): void
    {
        // Get a city with packages
        $florence = City::where('name', 'Florence')->first();

        // Get the packages
        $packages = $florence->packages()->get();

        // Fetch packages individually
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $europeanPackage = Package::where('title->en', 'European Discovery')->first();
        $worldPackage = Package::where('title->en', 'World Tour')->first();
        $florencePackage = Package::where('title->en', 'Florence Experience')->first();

        // Assert the city has the correct packages
        $this->assertTrue($packages->contains('id', $italianPackage->id));
        $this->assertTrue($packages->contains('id', $europeanPackage->id));
        $this->assertTrue($packages->contains('id', $worldPackage->id));
        $this->assertTrue($packages->contains('id', $florencePackage->id));

        // Assert the city has no other packages
        $this->assertEquals(4, $packages->count());
    }

    /**
     * Test region to package relationship returns correct number of packages
     */
    public function test_region_package_relationship_count(): void
    {
        // Get a region with packages
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Assert the region has 5 packages
        $this->assertEquals(5, $tuscany->packages->count());
    }

    /**
     * Test region to package bidirectional relationship through cities
     */
    public function test_region_package_relationship(): void
    {
        // Get a region with packages
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Get a package with cities
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get the regions through the package's cities
        $regions = $italianPackage->regions()->get();

        // Assert the package has regions
        $this->assertTrue($regions->count() > 0);

        // Assert the package is related to Tuscany region
        $this->assertTrue($regions->contains('id', $tuscany->id));

        // Get packages through the region's cities
        $packages = $tuscany->packages()->get();

        // Assert the region has packages
        $this->assertTrue($packages->count() > 0);

        // Assert the region is related to Italian Adventure
        $this->assertTrue($packages->contains('id', $italianPackage->id));
    }

    /**
     * Test country to package bidirectional relationship through cities
     */
    public function test_country_package_relationship(): void
    {
        // Get a country with packages
        $italy = Country::where('name', 'Italy')->first();

        // Get a package with cities
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get the countries through the package's cities
        $countries = $italianPackage->countries()->get();

        // Assert the package has countries
        $this->assertTrue($countries->count() > 0);

        // Assert the package is related to Italy
        $this->assertTrue($countries->contains('id', $italy->id));

        // Get packages through the country's cities
        $packages = $italy->packages()->get();

        // Assert the country has packages
        $this->assertTrue($packages->count() > 0);

        // Assert the country is related to Italian Adventure
        $this->assertTrue($packages->contains('id', $italianPackage->id));
    }

    /**
     * Test subregion to package bidirectional relationship through cities
     */
    public function test_subregion_package_relationship(): void
    {
        // Get a subregion with packages
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Get a package with cities
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get the subregions through the package's cities
        $subregions = $italianPackage->subregions()->get();

        // Assert the package has subregions
        $this->assertTrue($subregions->count() > 0);

        // Assert the package is related to Southern Europe
        $this->assertTrue($subregions->contains('id', $southernEurope->id));

        // Get packages through the subregion's cities
        $packages = $southernEurope->packages()->get();

        // Assert the subregion has packages
        $this->assertTrue($packages->count() > 0);

        // Assert the subregion is related to Italian Adventure
        $this->assertTrue($packages->contains('id', $italianPackage->id));

        // Assert that the subregion has the correct number of packages
//        $this->assertEquals(4, $southernEurope->packages()->count());
    }

    /**
     * Test continent to package bidirectional relationship through cities
     */
    public function test_continent_package_relationship(): void
    {
        // Get a continent with packages
        $europe = Continent::where('name', 'Europe')->first();

        // Get a package with cities
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get the continents through the package's cities
        $continents = $italianPackage->continents()->get();

        // Assert the package has continents
        $this->assertTrue($continents->count() > 0);

        // Assert the package is related to Europe
        $this->assertTrue($continents->contains('id', $europe->id));

        // Get packages through the continent's cities
        $packages = $europe->packages()->get();

        // Assert the continent has packages
        $this->assertTrue($packages->count() > 0);

        // Assert the continent is related to Italian Adventure
        $this->assertTrue($packages->contains('id', $italianPackage->id));
    }

    /**
     * Test eager loading capabilities with the refactored relationships
     */
    public function test_eager_loading_capabilities(): void
    {
        // Test that we can use the relationships in eager loading scenarios
        $europe = Continent::where('name', 'Europe')->first();

        // Get regions with their countries eager loaded
        $regions = $europe->regions()->with('country')->get();
        $this->assertTrue($regions->count() > 0);

        // Verify that countries are eager loaded (no additional queries)
        foreach ($regions as $region) {
            $this->assertTrue($region->relationLoaded('country'));
            $this->assertNotNull($region->country);
        }

        // Get cities with their regions eager loaded
        $cities = $europe->cities()->with('region')->get();
        $this->assertTrue($cities->count() > 0);

        // Verify that regions are eager loaded
        foreach ($cities as $city) {
            $this->assertTrue($city->relationLoaded('region'));
            $this->assertNotNull($city->region);
        }
    }

    /**
     * Test query builder methods work on relationship queries
     */
    public function test_query_builder_methods_on_relationships(): void
    {
        $europe = Continent::where('name', 'Europe')->first();

        // Test count() method
        $subregionsCount = $europe->subregions()->count();
        $this->assertGreaterThan(0, $subregionsCount);
        $countriesCount = $europe->countries()->count();
        $this->assertGreaterThan(0, $countriesCount);
        $regionsCount = $europe->regions()->count();
        $this->assertGreaterThan(0, $regionsCount);
        $citiesCount = $europe->cities()->count();
        $this->assertGreaterThan(0, $citiesCount);
        $packagesCount = $europe->packages()->count();
        $this->assertGreaterThan(0, $packagesCount);

        // Test exists() method
        $hasSubregions = $europe->subregions()->exists();
        $this->assertTrue($hasSubregions);
        $hasCountries = $europe->countries()->exists();
        $this->assertTrue($hasCountries);
        $hasRegions = $europe->regions()->exists();
        $this->assertTrue($hasRegions);
        $hasCities = $europe->cities()->exists();
        $this->assertTrue($hasCities);
        $hasPackages = $europe->packages()->exists();
        $this->assertTrue($hasPackages);

        // Test orderBy() method
        $orderedSubregions = $europe->subregions()->orderBy('name')->get();
        $this->assertTrue($orderedSubregions->count() > 0);
        $orderedCountries = $europe->countries()->orderBy('name')->get();
        $this->assertTrue($orderedCountries->count() > 0);
        $orderedRegions = $europe->regions()->orderBy('name')->get();
        $this->assertTrue($orderedRegions->count() > 0);
        $orderedCities = $europe->cities()->orderBy('name')->get();
        $this->assertTrue($orderedCities->count() > 0);
        $orderedPackages = $europe->packages()->orderBy('title->en')->get();
        $this->assertTrue($orderedPackages->count() > 0);

        // Test limit() method
        $limitedSubregions = $europe->subregions()->limit(1)->get();
        $this->assertEquals(1, $limitedSubregions->count());
        $limitedCountries = $europe->countries()->limit(1)->get();
        $this->assertEquals(1, $limitedCountries->count());
        $limitedRegions = $europe->regions()->limit(1)->get();
        $this->assertEquals(1, $limitedRegions->count());
        $limitedCities = $europe->cities()->limit(1)->get();
        $this->assertEquals(1, $limitedCities->count());
        $limitedPackages = $europe->packages()->limit(1)->get();
        $this->assertEquals(1, $limitedPackages->count());

        // Test pluck() method
        $subregionNames = $europe->subregions()->pluck('subregions.name');
        $this->assertTrue($subregionNames->count() > 0);
        $this->assertTrue($subregionNames->contains('Southern Europe'));
        $regionNames = $europe->regions()->pluck('regions.name');
        $this->assertTrue($regionNames->count() > 0);
        $this->assertTrue($regionNames->contains('Tuscany'));
        $cityNames = $europe->cities()->pluck('cities.name');
        $this->assertTrue($cityNames->count() > 0);
        $this->assertTrue($cityNames->contains('Florence'));
        $packageTitles = $europe->packages()->pluck('packages.title');
        $this->assertTrue($packageTitles->count() > 0);
        $this->assertTrue($packageTitles->contains('Italian Adventure'));
    }

    // ========== ALIAS TESTS ==========

    /**
     * Test continent alias functionality
     */
    public function test_continent_alias_functionality(): void
    {
        // Test getting continent by alias
        $europeByAlias = Alias::where('name', 'EU')->first()->aliasable;
        $this->assertInstanceOf(Continent::class, $europeByAlias);
        $this->assertEquals('Europe', $europeByAlias->name);

        // Test getting aliases from continent
        $europe = Continent::where('name', 'Europe')->first();
        $aliases = $europe->aliases;
        $this->assertTrue($aliases->count() > 0);
        $this->assertTrue($aliases->contains('name', 'EU'));
        $this->assertTrue($aliases->contains('name', 'Europa'));

        // Test packages through continent alias
        $packagesFromEuropeAlias = $europeByAlias->packages()->get();
        $this->assertTrue($packagesFromEuropeAlias->count() > 0);

        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $this->assertTrue($packagesFromEuropeAlias->contains('id', $italianPackage->id));
    }

    /**
     * Test subregion alias functionality
     */
    public function test_subregion_alias_functionality(): void
    {
        // Test getting subregion by alias
        $southernEuropeByAlias = Alias::where('name', 'Sud Europa')->first()->aliasable;
        $this->assertInstanceOf(Subregion::class, $southernEuropeByAlias);
        $this->assertEquals('Southern Europe', $southernEuropeByAlias->name);

        // Test getting aliases from subregion
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();
        $aliases = $southernEurope->aliases;
        $this->assertTrue($aliases->count() > 0);
        $this->assertTrue($aliases->contains('name', 'Sud Europa'));
        $this->assertTrue($aliases->contains('name', 'Mediterranean Europe'));

        // Test packages through subregion alias
        $packagesFromSubregionAlias = $southernEuropeByAlias->packages()->get();
        $this->assertTrue($packagesFromSubregionAlias->count() > 0);

        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $this->assertTrue($packagesFromSubregionAlias->contains('id', $italianPackage->id));
    }

    /**
     * Test country alias functionality
     */
    public function test_country_alias_functionality(): void
    {
        // Test getting country by alias
        $italyByAlias = Alias::where('name', 'Italia')->first()->aliasable;
        $this->assertInstanceOf(Country::class, $italyByAlias);
        $this->assertEquals('Italy', $italyByAlias->name);

        // Test getting aliases from country
        $italy = Country::where('name', 'Italy')->first();
        $aliases = $italy->aliases;
        $this->assertTrue($aliases->count() > 0);
        $this->assertTrue($aliases->contains('name', 'Italia'));
        $this->assertTrue($aliases->contains('name', 'IT'));
        $this->assertTrue($aliases->contains('name', 'Boot Country'));

        // Test packages through country alias
        $packagesFromCountryAlias = $italyByAlias->packages()->get();
        $this->assertTrue($packagesFromCountryAlias->count() > 0);

        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $this->assertTrue($packagesFromCountryAlias->contains('id', $italianPackage->id));
    }

    /**
     * Test region alias functionality
     */
    public function test_region_alias_functionality(): void
    {
        // Test getting region by alias
        $tuscanyByAlias = Alias::where('name', 'Toscana')->first()->aliasable;
        $this->assertInstanceOf(Region::class, $tuscanyByAlias);
        $this->assertEquals('Tuscany', $tuscanyByAlias->name);

        // Test getting aliases from region
        $tuscany = Region::where('name', 'Tuscany')->first();
        $aliases = $tuscany->aliases;
        $this->assertTrue($aliases->count() > 0);
        $this->assertTrue($aliases->contains('name', 'Toscana'));
        $this->assertTrue($aliases->contains('name', 'Heart of Italy'));

        // Test packages through region alias
        $packagesFromRegionAlias = $tuscanyByAlias->packages()->get();
        $this->assertTrue($packagesFromRegionAlias->count() > 0);

        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $this->assertTrue($packagesFromRegionAlias->contains('id', $italianPackage->id));
    }

    /**
     * Test city alias functionality
     */
    public function test_city_alias_functionality(): void
    {
        // Test getting city by alias
        $florenceByAlias = Alias::where('name', 'Firenze')->first()->aliasable;
        $this->assertInstanceOf(City::class, $florenceByAlias);
        $this->assertEquals('Florence', $florenceByAlias->name);

        // Test getting aliases from city
        $florence = City::where('name', 'Florence')->first();
        $aliases = $florence->aliases;
        $this->assertTrue($aliases->count() > 0);
        $this->assertTrue($aliases->contains('name', 'Firenze'));
        $this->assertTrue($aliases->contains('name', 'Cradle of Renaissance'));

        // Test packages through city alias
        $packagesFromCityAlias = $florenceByAlias->packages;
        $this->assertTrue($packagesFromCityAlias->count() > 0);

        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $this->assertTrue($packagesFromCityAlias->contains('id', $italianPackage->id));
    }

    /**
     * Test comprehensive alias chain - from continent alias to packages
     */
    public function test_alias_chain_continent_to_packages(): void
    {
        // Start with continent alias
        $europeByAlias = Alias::where('name', 'Old World')->first()->aliasable;
        $this->assertEquals('Europe', $europeByAlias->name);

        // Get subregions through continent alias
        $subregions = $europeByAlias->subregions;
        $this->assertTrue($subregions->count() > 0);

        // Get countries through continent alias
        $countries = $europeByAlias->countries;
        $this->assertTrue($countries->count() > 0);

        // Get regions through continent alias
        $regions = $europeByAlias->regions()->get();
        $this->assertTrue($regions->count() > 0);

        // Get cities through continent alias
        $cities = $europeByAlias->cities()->get();
        $this->assertTrue($cities->count() > 0);

        // Get packages through continent alias
        $packages = $europeByAlias->packages()->get();
        $this->assertTrue($packages->count() > 0);

        // Verify specific packages are included
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();
        $europeanPackage = Package::where('title->en', 'European Discovery')->first();
        $this->assertTrue($packages->contains('id', $italianPackage->id));
        $this->assertTrue($packages->contains('id', $europeanPackage->id));
    }

    /**
     * Test comprehensive alias chain - from city alias to continent
     */
    public function test_alias_chain_city_to_continent(): void
    {
        // Start with city alias
        $florenceByAlias = Alias::where('name', 'City of Lilies')->first()->aliasable;
        $this->assertEquals('Florence', $florenceByAlias->name);

        // Get region through city alias
        $region = $florenceByAlias->region;
        $this->assertEquals('Tuscany', $region->name);

        // Get country through city alias
        $country = $florenceByAlias->country;
        $this->assertEquals('Italy', $country->name);

        // Get subregion through city alias
        $subregion = $florenceByAlias->subregion;
        $this->assertEquals('Southern Europe', $subregion->name);

        // Get continent through city alias
        $continent = $florenceByAlias->continent;
        $this->assertEquals('Europe', $continent->name);
    }

    // ========== DISTINCT PACKAGE TESTS ==========

    /**
     * Test that region packages are distinct even when package is connected to multiple cities in the region
     */
    public function test_region_packages_are_distinct(): void
    {
        // Get Tuscany region which has Florence and Siena
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Get Italian Adventure package which is connected to both Florence and Siena
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get packages through the region
        $packages = $tuscany->packages()->get();

        // Count how many times Italian Adventure appears
        $italianPackageCount = $packages->where('id', $italianPackage->id)->count();

        // Assert it appears only once, not twice
        $this->assertEquals(1, $italianPackageCount, 'Italian Adventure package should appear only once in Tuscany region packages');

        // Also verify the total count is correct (5 unique packages)
        $this->assertEquals(5, $packages->count(), 'Tuscany should have exactly 5 unique packages');

        // Verify all package IDs are unique
        $packageIds = $packages->pluck('id')->toArray();
        $uniquePackageIds = array_unique($packageIds);
        $this->assertEquals(count($packageIds), count($uniquePackageIds), 'All package IDs should be unique');
    }

    /**
     * Test that country packages are distinct even when package is connected to multiple cities in the country
     */
    public function test_country_packages_are_distinct(): void
    {
        // Get Italy country which has Florence, Siena, and Milan
        $italy = Country::where('name', 'Italy')->first();

        // Get Italian Adventure package which is connected to Florence, Siena, and Milan
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get packages through the country
        $packages = $italy->packages()->get();

        // Count how many times Italian Adventure appears
        $italianPackageCount = $packages->where('id', $italianPackage->id)->count();

        // Assert it appears only once, not three times
        $this->assertEquals(1, $italianPackageCount, 'Italian Adventure package should appear only once in Italy country packages');

        // Verify all package IDs are unique
        $packageIds = $packages->pluck('id')->toArray();
        $uniquePackageIds = array_unique($packageIds);
        $this->assertEquals(count($packageIds), count($uniquePackageIds), 'All package IDs should be unique');
    }

    /**
     * Test that subregion packages are distinct even when package is connected to multiple cities in the subregion
     */
    public function test_subregion_packages_are_distinct(): void
    {
        // Get Southern Europe subregion which has Florence, Siena, and Milan through Italy
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();

        // Get Italian Adventure package which is connected to Florence, Siena, and Milan
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get packages through the subregion
        $packages = $southernEurope->packages()->get();

        // Count how many times Italian Adventure appears
        $italianPackageCount = $packages->where('id', $italianPackage->id)->count();

        // Assert it appears only once, not three times
        $this->assertEquals(1, $italianPackageCount, 'Italian Adventure package should appear only once in Southern Europe subregion packages');

        // Verify all package IDs are unique
        $packageIds = $packages->pluck('id')->toArray();
        $uniquePackageIds = array_unique($packageIds);
        $this->assertEquals(count($packageIds), count($uniquePackageIds), 'All package IDs should be unique');
    }

    /**
     * Test that continent packages are distinct even when package is connected to multiple cities in the continent
     */
    public function test_continent_packages_are_distinct(): void
    {
        // Get Europe continent which has multiple cities connected to the same packages
        $europe = Continent::where('name', 'Europe')->first();

        // Get Italian Adventure package which is connected to Florence, Siena, and Milan
        $italianPackage = Package::where('title->en', 'Italian Adventure')->first();

        // Get European Discovery package which is connected to Florence and Nice
        $europeanPackage = Package::where('title->en', 'European Discovery')->first();

        // Get World Tour package which is connected to Florence and Nice
        $worldPackage = Package::where('title->en', 'World Tour')->first();

        // Get packages through the continent
        $packages = $europe->packages()->get();

        // Count how many times each package appears
        $italianPackageCount = $packages->where('id', $italianPackage->id)->count();
        $europeanPackageCount = $packages->where('id', $europeanPackage->id)->count();
        $worldPackageCount = $packages->where('id', $worldPackage->id)->count();

        // Assert each appears only once
        $this->assertEquals(1, $italianPackageCount, 'Italian Adventure package should appear only once in Europe continent packages');
        $this->assertEquals(1, $europeanPackageCount, 'European Discovery package should appear only once in Europe continent packages');
        $this->assertEquals(1, $worldPackageCount, 'World Tour package should appear only once in Europe continent packages');

        // Verify all package IDs are unique
        $packageIds = $packages->pluck('id')->toArray();
        $uniquePackageIds = array_unique($packageIds);
        $this->assertEquals(count($packageIds), count($uniquePackageIds), 'All package IDs should be unique');
    }

    /**
     * Test that package collections are properly distinct and count correctly
     * Note: count() method on HasManyDeep relationships doesn't respect distinct(),
     * but get()->count() does, which is what we actually need for real usage.
     */
    public function test_package_collections_are_distinct(): void
    {
        // Test region packages
        $tuscany = Region::where('name', 'Tuscany')->first();
        $tuscanyPackages = $tuscany->packages()->get();
        $this->assertEquals(5, $tuscanyPackages->count(), 'Tuscany should have exactly 5 distinct packages');

        // Test country packages
        $italy = Country::where('name', 'Italy')->first();
        $italyPackages = $italy->packages()->get();
        $this->assertGreaterThan(0, $italyPackages->count(), 'Italy should have packages');

        // Test subregion packages
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();
        $southernEuropePackages = $southernEurope->packages()->get();
        $this->assertGreaterThan(0, $southernEuropePackages->count(), 'Southern Europe should have packages');

        // Test continent packages
        $europe = Continent::where('name', 'Europe')->first();
        $europePackages = $europe->packages()->get();
        $this->assertGreaterThan(0, $europePackages->count(), 'Europe should have packages');

        // Verify all collections have unique package IDs
        $this->assertEquals($tuscanyPackages->count(), $tuscanyPackages->unique('id')->count(), 'Tuscany packages should be unique');
        $this->assertEquals($italyPackages->count(), $italyPackages->unique('id')->count(), 'Italy packages should be unique');
        $this->assertEquals($southernEuropePackages->count(), $southernEuropePackages->unique('id')->count(), 'Southern Europe packages should be unique');
        $this->assertEquals($europePackages->count(), $europePackages->unique('id')->count(), 'Europe packages should be unique');
    }

    /**
     * Test that distinct works with query builder methods
     */
    public function test_distinct_packages_with_query_builder_methods(): void
    {
        $tuscany = Region::where('name', 'Tuscany')->first();

        // Test with orderBy
        $orderedPackages = $tuscany->packages()->orderBy('title->en')->get();
        $packageIds = $orderedPackages->pluck('id')->toArray();
        $uniquePackageIds = array_unique($packageIds);
        $this->assertEquals(count($packageIds), count($uniquePackageIds), 'Ordered packages should still be distinct');

        // Test with limit
        $limitedPackages = $tuscany->packages()->limit(3)->get();
        $limitedPackageIds = $limitedPackages->pluck('id')->toArray();
        $uniqueLimitedPackageIds = array_unique($limitedPackageIds);
        $this->assertEquals(count($limitedPackageIds), count($uniqueLimitedPackageIds), 'Limited packages should still be distinct');

        // Test with pluck
        $packageTitles = $tuscany->packages()->pluck('packages.title');
        $this->assertTrue($packageTitles->count() > 0, 'Should be able to pluck from distinct packages');

        // Test with exists
        $hasPackages = $tuscany->packages()->exists();
        $this->assertTrue($hasPackages, 'Should be able to check existence of distinct packages');
    }
}
